import { useState, useEffect } from "react";
import RightSideModal from "../RightSideModal";
import ClinicDetailsModal from "./ClinicDetailsModal";
import { Calendar } from "Components/Calendar";
import ClinicsFilterHeader from "./ClinicsFilterHeader";
import ClinicCard from "./ClinicCard";

export default function CalendarTab({
  programs,
  fetchClinics,
  FiltersContent,
  filters,
  setFilters,
  clearFilters,
  clubProfile,
  fetchCoachesForClinic,
}) {
  const [availableOnly, setAvailableOnly] = useState(false);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedClinic, setSelectedClinic] = useState(null);
  const [clinics, setClinics] = useState(programs);
  const [filterLoading, setFilterLoading] = useState(false);
  const [sortOrder, setSortOrder] = useState("desc");
  const [showSortOptions, setShowSortOptions] = useState(false);
  const sortClinics = (order) => {
    const sortedClinics = [...clinics].sort((a, b) => {
      const dateA = new Date(a.clinic_date + " " + a.clinic_start_time);
      const dateB = new Date(b.clinic_date + " " + b.clinic_start_time);
      return order === "asc" ? dateA - dateB : dateB - dateA;
    });
    setClinics(sortedClinics);
    setSortOrder(order);
    setShowSortOptions(false);
  };

  useEffect(() => {
    let filteredPrograms = [...programs];

    // Apply date filter if a date is selected
    if (selectedDate) {
      const selectedDateStr = selectedDate.toISOString().split("T")[0];
      filteredPrograms = filteredPrograms.filter((clinic) => {
        return clinic.clinic_date === selectedDateStr;
      });
    }

    // Apply available slots filter
    if (availableOnly) {
      filteredPrograms = filteredPrograms.filter(
        (clinic) => parseInt(clinic.slots_remaining) > 0
      );
    }

    // Sort the filtered programs
    const sortedPrograms = filteredPrograms.sort((a, b) => {
      const dateA = new Date(a.clinic_date + " " + a.clinic_start_time);
      const dateB = new Date(b.clinic_date + " " + b.clinic_start_time);
      return sortOrder === "asc" ? dateA - dateB : dateB - dateA;
    });

    setClinics(sortedPrograms);
  }, [programs, sortOrder, selectedDate, availableOnly]);

  const getActiveFiltersCount = () => {
    const selectedDays = Object.values(filters.days).filter(Boolean).length;
    const selectedTimes = Object.values(filters.timeOfDay).filter(
      Boolean
    ).length;
    const hasPriceFilter = filters.price.from || filters.price.to ? 1 : 0;

    return selectedDays + selectedTimes + hasPriceFilter;
  };

  const handleDateClick = async (day) => {
    if (!day) return;

    try {
      // Create date at start of day to avoid timezone issues
      const newSelectedDate = new Date(
        currentMonth.getFullYear(),
        currentMonth.getMonth(),
        day,
        0,
        0,
        0
      );

      setSelectedDate(newSelectedDate);

      // Format date as YYYY-MM-DD using UTC to avoid timezone shifts
      const formattedDate = newSelectedDate.toISOString().split("T")[0];

      let filterParams = [];
      filterParams.push(`start_date=${formattedDate}`);
      filterParams.push(`end_date=${formattedDate}`);

      // Add day filters
      const selectedDays = Object.entries(filters.days)
        .filter(([_, isSelected]) => isSelected)
        .map(([day]) => day.toLowerCase());

      if (selectedDays.length > 0) {
        filterParams.push(`weekday=${selectedDays.join(",")}`);
      }

      // Add time filters
      const selectedTimes = Object.entries(filters.timeOfDay)
        .filter(([_, isSelected]) => isSelected)
        .map(([time]) => time.toLowerCase());

      if (selectedTimes.length > 0) {
        filterParams.push(`times=${selectedTimes.join(",")}`);
      }

      const filterString = filterParams.join("&");
      // Call fetchClinics with the filter string to get updated data from API
      await fetchClinics(formattedDate, false, filterString);
    } catch (error) {
      console.error("Error handling date selection:", error);
    }
  };

  // Update clear date filter function to use API
  const clearDateFilter = async () => {
    setSelectedDate(null);

    // Get current filters without date
    const selectedDays = Object.entries(filters.days)
      .filter(([_, isSelected]) => isSelected)
      .map(([day]) => day.toLowerCase());

    const selectedTimes = Object.entries(filters.timeOfDay)
      .filter(([_, isSelected]) => isSelected)
      .map(([time]) => time.toLowerCase());

    let filterParams = [];

    if (selectedDays.length > 0) {
      filterParams.push(`weekday=${selectedDays.join(",")}`);
    }

    if (selectedTimes.length > 0) {
      filterParams.push(`times=${selectedTimes.join(",")}`);
    }

    const filterString = filterParams.join("&");
    // Call fetchClinics without date filter
    await fetchClinics(null, false, filterString);
  };

  const handleApplyFilters = async () => {
    setFilterLoading(true);
    let filterParams = [];

    // Add date filter if selected
    if (selectedDate) {
      const formattedDate = selectedDate.toISOString().split("T")[0];
      filterParams.push(`start_date=${formattedDate}`);
      filterParams.push(`end_date=${formattedDate}`);
    }

    // Add day filters
    const selectedDays = Object.entries(filters.days)
      .filter(([_, isSelected]) => isSelected)
      .map(([day]) => day.toLowerCase());

    if (selectedDays.length > 0) {
      filterParams.push(`weekday=${selectedDays.join(",")}`);
    }

    // Add time filters
    const selectedTimes = Object.entries(filters.timeOfDay)
      .filter(([_, isSelected]) => isSelected)
      .map(([time]) => time.toLowerCase());

    if (selectedTimes.length > 0) {
      filterParams.push(`times=${selectedTimes.join(",")}`);
    }

    const filterString = filterParams.join("&");
    await fetchClinics(
      selectedDate ? selectedDate.toISOString().split("T")[0] : null,
      false,
      filterString
    );
    setIsFilterModalOpen(false);
    setFilterLoading(false);
  };

  const toggleAvailableSlots = () => {
    setAvailableOnly(!availableOnly);
    // The filtering will be handled by the useEffect hook
  };

  const handlePreviousMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.setMonth(currentMonth.getMonth() - 1))
    );
  };

  const handleNextMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.setMonth(currentMonth.getMonth() + 1))
    );
  };

  return (
    <div>
      <div className="mx-auto max-w-6xl p-2 sm:p-4">
        <div className="flex flex-col gap-4 sm:gap-6 md:flex-row md:gap-8">
          {/* Calendar Section */}
          <div className="h-fit w-full rounded-lg bg-white p-3 shadow-sm sm:p-4 sm:shadow-5 md:w-[350px] lg:w-[400px]">
            <Calendar
              clinics={programs}
              currentMonth={currentMonth}
              selectedDate={selectedDate}
              onDateClick={handleDateClick}
              onPreviousMonth={handlePreviousMonth}
              onNextMonth={handleNextMonth}
              onDateSelect={(date) => {
                if (date) {
                  handleDateClick(date.getDate());
                }
              }}
              daysOff={(() => {
                try {
                  return clubProfile?.days_off
                    ? JSON.parse(clubProfile.days_off)
                    : [];
                } catch (error) {
                  console.error("Error parsing days_off:", error);
                  return [];
                }
              })()}
            />
            {selectedDate && (
              <div className="mt-3 flex flex-wrap items-center justify-between border-t border-gray-200 pt-3 sm:mt-4 sm:pt-4">
                <span className="mr-2 text-xs text-gray-600 sm:text-sm">
                  Showing clinics for{" "}
                  {selectedDate.toLocaleDateString("en-US", {
                    month: "long",
                    day: "numeric",
                    year: "numeric",
                  })}
                </span>
                <button
                  onClick={clearDateFilter}
                  className="text-xs text-blue-600 hover:underline sm:text-sm"
                >
                  Clear
                </button>
              </div>
            )}
          </div>

          {/* Clinic Cards Section */}
          <div className="w-full">
            <div className="space-y-3 rounded-lg bg-white p-3 shadow-sm sm:space-y-4 sm:p-5">
              <ClinicsFilterHeader
                getActiveFiltersCount={getActiveFiltersCount}
                clearFilters={clearFilters}
                setIsFilterModalOpen={setIsFilterModalOpen}
                availableOnly={availableOnly}
                toggleAvailableSlots={toggleAvailableSlots}
                sortOrder={sortOrder}
                showSortOptions={showSortOptions}
                setShowSortOptions={setShowSortOptions}
                sortClinics={sortClinics}
              />
              {clinics.length === 0 ? (
                <div className="flex flex-col items-center justify-center rounded-lg bg-gray-50 py-8 sm:py-12">
                  <div className="mb-4 rounded-full bg-gray-100 p-3">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="text-gray-400"
                    >
                      <path
                        d="M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                  <h3 className="mb-1 text-center text-base font-medium text-gray-900 sm:text-lg">
                    No clinics found
                  </h3>
                  <p className="px-4 text-center text-xs text-gray-500 sm:text-sm">
                    {availableOnly
                      ? "There are no clinics with available slots for the selected filters."
                      : "There are no clinics matching your selected filters."}
                  </p>
                  {(getActiveFiltersCount() > 0 ||
                    availableOnly ||
                    selectedDate) && (
                    <button
                      onClick={async () => {
                        await clearFilters();
                        setAvailableOnly(false);
                        setSelectedDate(null);
                      }}
                      className="mt-4 text-xs font-medium text-blue-600 hover:text-blue-700 sm:text-sm"
                    >
                      Clear all filters
                    </button>
                  )}
                </div>
              ) : (
                clinics.map((clinic) => (
                  <div
                    key={clinic.id}
                    onClick={() => setSelectedClinic(clinic)}
                  >
                    <ClinicCard clinic={clinic} />
                  </div>
                ))
              )}
            </div>
            <RightSideModal
              isOpen={isFilterModalOpen}
              onClose={() => setIsFilterModalOpen(false)}
              title="Filters"
              primaryButtonText="Apply and close"
              onPrimaryAction={handleApplyFilters}
              className="bg-gray-100"
              submitting={filterLoading}
            >
              <FiltersContent filters={filters} setFilters={setFilters} />
            </RightSideModal>
            <ClinicDetailsModal
              isOpen={selectedClinic !== null}
              onClose={() => setSelectedClinic(null)}
              clinic={selectedClinic}
              fetchCoachesForClinic={fetchCoachesForClinic}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
