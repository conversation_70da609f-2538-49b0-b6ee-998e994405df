import React, { useState, useRef, useEffect } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";

/**
 * A reusable coach selection modal component
 * @param {Object} props
 * @param {Array} props.coaches - Array of coach objects
 * @param {Array} props.selectedCoaches - Array of selected coach objects
 * @param {Function} props.setSelectedCoaches - Function to update selected coaches
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to close the modal
 * @param {Function} props.onSave - Function to handle saving the selected coaches
 * @param {boolean} props.loading - Whether a coach is being added
 * @returns {JSX.Element}
 */
const CoachSelectionModal = ({
  coaches = [],
  selectedCoaches = [],
  setSelectedCoaches,
  isOpen,
  onClose,
  onSave,
  loading,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const searchInputRef = useRef(null);

  // Focus the search input when the modal opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      // Use a small timeout to ensure the modal is fully rendered
      setTimeout(() => {
        searchInputRef.current.focus();
      }, 50);
    }
  }, [isOpen]);
  console.log("coaches", coaches);
  // Filter coaches based on search query
  const filteredCoaches = coaches.filter((coach) => {
    // Handle both data structures: coach with user property or coach with direct properties
    const firstName = coach.user?.first_name || coach.first_name || "";
    const lastName = coach.user?.last_name || coach.last_name || "";
    const fullName = `${firstName} ${lastName}`.toLowerCase();
    return fullName.includes(searchQuery.toLowerCase());
  });

  // Toggle coach selection
  const toggleCoach = (coach) => {
    setSelectedCoaches((prev) =>
      prev.find((c) => c.id === coach.id)
        ? prev.filter((c) => c.id !== coach.id)
        : [...prev, { ...coach, hours: [] }]
    );
  };

  // Check if a coach is selected
  const isSelected = (coachId) => selectedCoaches.some((c) => c.id === coachId);

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-96 rounded-lg bg-white shadow-lg">
        <div className="mb-0 flex items-center justify-between border-b p-4">
          <h3 className="text-lg font-medium">Add coaches</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
        <div className="p-4">
          <div className="mb-4">
            <div className="flex items-center gap-1 rounded-lg border border-gray-300 px-2 focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500">
              <span className="w-5">
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z"
                    fill="#525866"
                  />
                </svg>
              </span>
              <input
                ref={searchInputRef}
                type="text"
                placeholder="search by name"
                value={searchQuery}
                onChange={handleSearchChange}
                className="w-full border-none bg-transparent py-2 focus:outline-none focus:ring-0"
              />
            </div>
          </div>
          {selectedCoaches.length > 0 && (
            <div className="mb-4 flex flex-wrap gap-2">
              {selectedCoaches.map((coach) => (
                <div
                  key={coach.id}
                  className="flex items-center gap-2 rounded-lg bg-gray-100 px-3 py-1"
                >
                  <div className="h-6 w-6 overflow-hidden rounded-full bg-gray-200">
                    <img
                      src={
                        coach?.user?.photo ||
                        coach?.photo ||
                        "/default-avatar.png"
                      }
                      alt={`${
                        coach?.user?.first_name || coach?.first_name || ""
                      } ${coach?.user?.last_name || coach?.last_name || ""}`}
                      className="h-full w-full object-cover"
                      loading="lazy"
                    />
                  </div>
                  <span className="text-sm">{`${
                    coach?.user?.first_name || coach?.first_name || ""
                  } ${coach?.user?.last_name || coach?.last_name || ""}`}</span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleCoach(coach);
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          )}
          <div className="max-h-64 space-y-2 overflow-y-auto rounded-xl bg-gray-50">
            {filteredCoaches.length > 0 &&
              filteredCoaches.map((coach) => (
                <div
                  key={coach.id}
                  onClick={() => toggleCoach(coach)}
                  className="flex cursor-pointer items-center space-x-3 rounded-lg p-2 hover:bg-gray-50"
                >
                  <input
                    type="checkbox"
                    checked={isSelected(coach.id)}
                    onChange={() => {}}
                    className="h-4 w-4 rounded border-gray-300 text-blue-600"
                  />
                  <div className="h-8 w-8 overflow-hidden rounded-full bg-gray-200">
                    <img
                      src={
                        coach?.user?.photo ||
                        coach?.photo ||
                        "/default-avatar.png"
                      }
                      alt={`${
                        coach?.user?.first_name || coach?.first_name || ""
                      } ${coach?.user?.last_name || coach?.last_name || ""}`}
                      className="h-full w-full object-cover"
                      loading="lazy"
                    />
                  </div>
                  <span>{`${coach.user?.first_name || coach.first_name || ""} ${
                    coach.user?.last_name || coach.last_name || ""
                  }`}</span>
                </div>
              ))}
            {!filteredCoaches.length && (
              <p className="text-center text-sm text-gray-500">
                No coaches found
              </p>
            )}
          </div>

          <div className="mt-4 flex justify-between gap-3 pt-3">
            <button
              onClick={onClose}
              className="flex-1 rounded-xl border border-gray-300 px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              Cancel
            </button>
            <button
              onClick={onSave}
              disabled={loading}
              className="flex-1 rounded-xl bg-blue-900 px-4 py-2 text-white hover:bg-blue-800 disabled:opacity-50"
            >
              Save and close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CoachSelectionModal;
